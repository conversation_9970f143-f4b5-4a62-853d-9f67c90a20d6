<!-- (c) https://github.com/MontiCore/monticore -->

(c) Chair of Software Engineering, RWTH Aachen University


* Prof<PERSON> <PERSON>
* <PERSON>, <PERSON><PERSON><PERSON>.
* <PERSON><PERSON><PERSON>, M.Sc.
* <PERSON>, M.Sc.

# SLE Exercise Sheet 6: MontiGem

## Organizational Issues

Welcome to exercise sheet 6 for Software Language Engineering!
Please commit your solutions to the default branch of your exercise repository

> https://git.rwth-aachen.de/se-student/ss25/lectures/sle/exercises/exercise-group-73

### Submission Deadline

You can find the submission deadline on Moodle

> https://moodle.rwth-aachen.de/course/section.php?id=425093

Do __not__ do a Git rebase of your submitted commit after the submission
deadline. A rebase adds a later timestamp that makes the commit appear to be
handed in too late.

### Automatic Grading

We grade your submission automatically. Therefore, only submit parsing models,
compiling classes, and building projects. Furthermore, follow the instructions
for naming, creating, and modifying files and elements carefully. Do not change
method declarations if this is not explicitly stated. Otherwise, we cannot grade
the corresponding task or even the whole exercise sheet, and you will receive no
points. The status of your GitLab pipelines indicates if your submission may be
in the correct format, i.e., if your models are syntactically correct, the Java
code compiles, and the validation tests succeed. A failing pipeline indicates a
wrong format. However, a succeeding pipeline may not indicate that everything
works.

> https://git.rwth-aachen.de/se-student/ss25/lectures/sle/exercises/exercise-group-73/-/pipelines

### Dealing with Errors

You might encounter unique errors when setting up or running Git, Java, or
Gradle for the first time. If you are stuck, you can solve most problems by
searching in the search engine of your choice. Most error messages contain one
or two relevant lines that get to the heart of the problem. In forums such as
Stackoverflow, these errors have often been discussed, and various help has been
given.

If you cannot solve problems with Git, you can also publish changes via GitLab's
web editor:

> https://docs.gitlab.com/ee/user/project/repository/web_editor.html

If you encounter errors with the exercises or their tools, you may use the
[forum on Moodle](https://moodle.rwth-aachen.de/mod/forum/view.php?id=1858544)
to raise technical questions. In this case, please provide a
complete link to the concerning code and, if available, the complete stacktrace
as separate `.log` or `.txt` file.

If you are unfamiliar with the languages used in the exercises or how they
report errors, you might solve the exercises in small increments by changing the
models or source files and follow the instructions to verify your solution.
Thereby, you identify errors early, delimit them better, and you prevent
cascading errors.


## Exercise 6.1: Intro (0/100 Points)

In this exercise sheet, we introduce and combine CD-Merge and MontiGem to
generate a web application of a car rental system.

The [CD-Merge][cd-merge] tool merges multiple input class diagrams into one
considering the model elements and their semantics. For example, the tool merges
classes of the same name including their attributes and associations or erases
attributes already present in a parent class. This approach enables the creation
of a unified domain model from multiple sources, allowing different developer
teams or domain experts to collaborate and make modifications. Each source
provides one view of the complete system. In contrast to other tools, CD-Merge
combines views instead of extracting views from a complete model. You can read
more about CD-Merge on:

> https://github.com/MontiCore/cd4analysis/blob/dev/cdmerge/index.md

[UMLP][umlp] is a subset of the modeling languages of the UML with a particular
focus on applicability for programming and modeling of software systems.
MontiGem generates data-centric web applications from this subset, in particular
GUI models, class, and object diagrams. In this exercise sheet, the tooling
takes the merged class diagram alongside complementary models as input and
generates the web frontend and the server backend of the car rental application.
You can find additional information on writing UMLP-specific class diagrams on:

> https://www.se-rwth.de/sle_material/SLE01.3.CD4Analysis/

<p style="text-align: center;">
 <img src="images/renting-feature-diagram.svg" alt="welcome-screen" style="width: 40%;">
</p>

The feature diagram provides an overview of the relevant class diagrams and
potential configurations to generate a renting system. The partial class
diagrams are available in the [model-components](car-rental/model-components)
directory. The `Renting` class diagram is present in every configuration. It
defines base classes for rentable items, their status, rental transactions, and
customers. The `CustomerService` details the attributes of customers. A renting
application should include at least one domain of rentable items. In this case,
the domains are given by the `Cars` and the hypothetical `Books` class diagrams.
The class diagram for the car domain defines classes for mileage data points
that are accessed each day at 12 pm. The `Car` class extends the `RentableItem`
class defined by the `Renting` class diagram. Therefore, the `Cars` class
diagram declares the `RentableItem` class to be valid. The `Trucks` class
diagram extends the car domain and includes a `Vehicle` class that now both
`Car` and `Truck` extend. The yet empty `Booking` class diagram is a
non-exclusive alternative.

## Exercise 6.2: Building the Application (0/100 Points)

Before you start with the exercise and modify the input class diagrams, you may
build the web application and try it out doing the following steps:

* __Merge__ the input class diagrams by running
  ```shell
  gradle -p car-rental :backend:mergeCarRental
  ```

  Expected terminal output:
  ```
  Starting Gradle Daemon...
  Gradle Daemon started in 3 s 535 ms
  
  > Task :exercise-sheet-2:car-rental:backend:mergeCarRental
  ...
  [INFO]  umlp.UMLPTool ========= CD after 'UMLImportTrafo': =========
  ...
  
  BUILD SUCCESSFUL in 40s
  ```
* __Inspect__ the complete car rental model
  `car-rental/backend/build/CarRental.umlp`.
* __Build and start__ the backend of the web application locally by running
  ```shell
  gradle -p car-rental :backend:run
  ```

  Expected terminal output:
  ```
  [main] INFO org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8081 (http) with context path '/umlp/api'
  [main] INFO carrental.CarRentalServerApplication - Started CarRentalServerApplication in 4.493 seconds (JVM running for 4.828)
  shell:>
  ```
  Keep the process running.
* __Build and start__ the frontend of the web application locally by running
  ```shell
  gradle -p car-rental :frontend:runApp
  ```

  Expected terminal output:
  ```
  ** Angular Live Development Server is listening on localhost:4200, open your browser on http://localhost:4200/ **

  ✔ Compiled successfully.
  ```
  There may be multiple lines following `Compiled successfully`.
* __Keep__ the process running.
* __Open__ the web application using
  > http://localhost:4200/gui/Dashboard
* __Explore__ the web application. It provides a dashboard that displays
  statistics of the available data, lists of existing cars, and a page to create
  new car entries. When creating new cars, fill out all form elements including
  the sample image.
* __Terminate__ the `backend:run` and `frontend:runApp` task before you start a
  new build. Otherwise, your port may still be occupied.
* You can also start the backend and frontend simultaneously by running
    ```shell
  gradle -p car-rental runAll
  ```

The web interface is only for demonstration purposes. Using it does not award
any points. However, you can build it, particularly the backend, to check for
syntactical and contextual correctness of your models and implementation.

Alternatively, you may run your project online:

* __Push__ your changes to the default branch of the remote repository. This
  triggers a GitLab pipeline that builds and deploys your web application.
* __Check__ the pipeline's status by opening
  > https://git.rwth-aachen.de/se-student/ss25/lectures/sle/exercises/exercise-group-73/-/pipelines
* __Wait__ for the pipeline to complete _successfully_, this may take several
  minutes.
* __Access__ the project ID of your repository
  > https://git.rwth-aachen.de/se-student/ss25/lectures/sle/exercises/exercise-group-73

  by copying it from the `More Actions` overlay of your GitLab project.
  <p style="text-align: center;">
    <img src="images/project-id.png" alt="welcome-screen" style="width: 40%;">
  </p>
* __Open__ the hosted web frontend where `GITLAB_PROJECT_ID` is your copied
  GitLab project ID.
  > https://studentprojects.se-rwth.de/sle25/GITLAB_PROJECT_ID/gui/Dashboard
* __Access__ the frontend using the username `umlp` and `UECM3PG78TgTjLxsSCcE`.
  However, do not rely on the persistence of the database. Each new deployment
  of application or server restart erases the application's data.

[umlp]: https://www.se-rwth.de/research/UML-P/

[cd-merge]: https://www.se-rwth.de/publications/CDMerge-Semantically-Sound-Merging-of-Class-Diagrams-for-Software-Component-Integration.pdf

## Exercise 6.3: Car Rental (40/100 Points)

These exercises ask you to modify some class diagrams. Modify and test your
solutions incrementally to identify errors early.

### Exercise 6.3 a) Renting (15/40 Points)

<!-- Links: -->
[Renting]: car-rental/model-components/Renting.umlp
[CustomerService]: car-rental/model-components/CustomerService.umlp
[Cars]: car-rental/model-components/Cars.umlp
[Books]: car-rental/model-components/Books.umlp

Verify your modifications regularly by running
```shell
gradle -p car-rental :backend:test --tests RentingVerificationTest
```

First, we wish to extend the capabilities of the rental service itself.
For this purpose, modify the class diagram [`Renting`][Renting]:

* As it is not only important to store the date when a car is rented, but also
  when it is returned, connect an optional `returnDate` of type `LocalDateTime`
  to the rental transaction class using an association. Also add the role name
  `transaction` and `returnDate`. An additional tool uses the role names to
  generate builder classes for initializing data.
* The `RentalService` manages multiple `RentalTransaction`s and their payment.
  Therefore, add the `transactionManagement` association in which one object of
  role `rental` manages multiple objects of role `transaction`.
* Additionally, add a `processPayment` method to the `RentalService`. It
  receives a rental transaction and has no return type.

### Exercise 6.3 b) Customer Service (5/40 Points)

Verify your modifications regularly by running
```shell
gradle -p car-rental :backend:test --tests CustomerServiceVerificationTest
```

Modify the class diagram [`CustomerService`][CustomerService]:

* We wish to address Customers by their `email`, thus add it as a `String`
  attribute to the `Customer` class. Afterward, you must add an `email` address
  to the customer `jim` of the [CarData.od](car-rental/models/CarData.od) object
  diagram before building the app again.
  ```
  jim : Customer {
    name = "Jim Douglas";
    email = "<EMAIL>";
  };
  ```
* Due to a planned extension of the system, it is critically important that it
  is possible to treat some Customers as VIPs. Thus, add a new
  class `VIPStatus`, with a `String` attribute `personalGreeting`, such that
  personalized customized greetings are available to the car rentals most
  redeemed customers.
* Add an association to the `Customer` class, such that they either have one VIP
  status or none. Make sure to use the appropriate cardinality for this
  circumstance.

### Exercise 6.3 c) Trucks (20/40 Points)

Verify your modifications regularly by running
```shell
gradle -p car-rental :backend:test --tests TrucksVerificationTest
```

Next, we want to add trucks as an alternative form of vehicle that can be rented
via the car rental service. We do __NOT__ wish to modify the class diagram
[`Cars`][Cars], so instead we add a new class diagram `Trucks`:

* A `Truck` is similar to a `Car`. Thus, add a new abstract superclass `Vehicle`
  that extends the `RentableItem` class. The classes `Truck` and`Car` then
  extend `Vehicle`. `CDMerge` will then overwrite the inheritance hierarchy
  defined in `Cars`, accordingly.
* We would also like to move the `model`, `maker`, and `costPerDay` attributes
  from `Car` to `Vehicle`, if the  `Trucks` class diagram is merged. For this,
  copy those attributes from the class diagram [`Cars`][Cars] and add them to
  `Vehicle`. `CDMerge` will then ensure that there is no redundancy in the final
  model.
* For regulatory and safety reasons, it is necessary to disclose the maximal
  carrying capacity of a `Truck`. Therefore, add an additional `float` attribute
  `tonnage`.
* Car rental services want to track the approximate position of rented trucks.
  Therefore, add the `longitude` and `latitude` attributes of type `float` to
  a new class of type `TruckMileageDataPoint` that extends `MileageDataPoint`. A
  truck should have access to their mileage data points. The
  `TruckMileageDataPoint` side of such association should have the role name
  `dataPoint` and should only target the data point.
* As both cars and trucks need maintenance from time to time, during which
  they cannot be rented nor are available, add an option `MAINTENANCE` to the
  enum `RentableStatus` without modifying the class diagram [`Renting`][Renting]
  by exploiting `CDMerge`'s ability to merge types. Therefore, modify the class
  diagram that fits the additional `RentableStatus` best.

## Exercise 6.4: Derived Attributes (10/100 Points)

UMLP class diagrams provide the functionality to define so-called derived
attributes. Their value depends on other attributes and is calculated whenever
it gets accessed. Therefore, it is not persistently saved in the database. The
logic to calculate the value of a derived attribute is not encoded in the models
but in methods of Java source files.

With the help of the TOP mechanism, you can extend the generated artifacts by
handwritten code to calculate the derived attributes. The TOP mechanism checks
for each class file that needs to be generated if a file with the same name and
package exists in the handwritten source set. If this is the case, it appends
the `TOP` suffix to the generated file name. The handwritten class has to
provide the same interface as the generated one. This is typically achieved by
extending the TOP class. By this, the handwritten class may override the
behavior of already generated methods, add additional ones or even new
attributes. Since the generated and handwritten source code is split into
distinct files, regenerating code does not override the handwritten one.

> The application will not provide feedback on if your implementations are correct.

### Exercise 6.4 a) Availability (4/10 Points)

Implement the `isAvailable` method of the [carrental.RentableItem][RentableItem]
class that is part of the `backend` subproject. It should return `1` if and only
if the `status` is `AVAILABLE`. Otherwise, it should return `0`. You can
access the status via its automatically generated get method
`getStatus()`. The generator directly translated `RentableStatus` enum
of the class diagram into a Java enum.

Verify your solution by running
```shell
gradle -p car-rental :backend:compileJava
```

[RentableItem]: car-rental/backend/src/main/java/carrental/RentableItem.java

### Exercise 6.4 b) Mileage (6/10 Points)

Implement the `getMileage` method of the [`Car`][Car] class. The derived
`mileage` attribute should have the value of the highest data mileage point. If
non exists, it should return 0.

Verify your solution by running
```shell
gradle -p car-rental :backend:compileJava
```

[Car]: car-rental/backend/src/main/java/carrental/Car.java

## Exercise 6.5: Object Diagrams (40/100 Points)

Verify your modifications regularly by running
```shell
gradle -p car-rental :backend:test --tests ObjectDiagramVerificationTest
```

The object diagram [CarData][CarData] models the initial situation of the car
rental service, e.g., the data transferred from a legacy system. The diagram
models the iconic VW Käfer Herbie, a Ford Mustang, and their mileage data points
from 04.09.2024 to 07.09.2024. Jim Douglas rented the Mustang during this
period. Complete the object diagram to reflect the following events within this
timespan as presented by the system on 07.09.2024 at 16:00.

[CarData]: car-rental/models/CarData.od

### Toyota Corolla

* The car rental service owns a Toyota Corolla with a 25% discount compared to
  the rental price of the Ford Mustang.
* __IMPORTANT:__ Name the modeled object of this car `toyota`.
* Choose a suitable name for its name attribute.
* On 04.09.2024 at 11:00, the car rental manager drove it to the maintenance
  service.
* Afterward, at 11:59, it has a mileage count of 4008 miles.
* During maintenance, the mileage count was not increased.
* The manager started driving the vehicle back to the rental service that is 20
  miles away on 06.09.2024 at 16:00 and arrived the same day.
* Afterward, the vehicle is rentable again.

### Mercedes-Benz Actros

* The car rental purchased the Mercedes-Benz truck model Actros.
* It costs 200€ per day and has a capacity of 20 tons.
* The VIP customer Werner Ernst started renting the truck on 04.09.2024 at
  19:00. Till now, the total costs are 800€. He is a VIP customer and the car
  rental's favorite truck driver, which rewards him with a corresponding
  greeting.
  > When modeling class instances, MontiGem requires non-`null` attributes. You
  can use Java's [java.lang.Optional][Optional] class to model absent attributes
  to be non-`null`.
* Add the following data points to the truck:

   | Record Date         | Mileage | Longitude | Latitude |
   |---------------------|---------|-----------|----------|
   | 2024-09-04 12:00:00 | 13585.0 | 10.0157   | 48.7758  |
   | 2024-09-05 12:00:00 | 13645.0 | 10.0567   | 48.8001  |
   | 2024-09-06 12:00:00 | 13723.0 | 10.0982   | 48.8344  |
   | 2024-09-07 12:00:00 | 13783.0 | 10.1402   | 48.8550  |

[Optional]: https://docs.oracle.com/javase/8/docs/api/java/util/Optional.html

Name the modeled object of this truck `mercedes`, the object of the customer
`werner`, the object of the VIP-status `vip`.

When using the app, only the original cars and their mileage are displayed.

## Exercise 6.6: Book Rental (10/100 Points)

Be creative and model a Book Renting service by adding classes, attributes, and
associations to the class diagram [`Books`][Books]:

* Add at least two attributes to the `Book` class.
* Add at least one non-empty enum.
* Connect one enum via an association to the `Book` class.
* Add at least one new class containing at least one method and one attribute.
* Add one association using role names, cardinalities, and a specified
  navigation direction.

Check whether your model can be combined with the class diagrams
[`Renting`][Renting] and [`CustomerService`][CustomerService] by running 
```shell
gradle -p car-rental :backend:test --tests BooksVerificationTest
```

## Exercise 6.7: Provide Feedback (0/100)

Please edit the [feedback.md](feedback.md) file and give us your honest feedback
on this exercise sheet. This feedback is optional and will not influence your
grade. But instead, help us to improve this exercise for the following years.

You might reflect on the _comprehensibility_, _difficulty_, and _extent_ of the
exercises.
