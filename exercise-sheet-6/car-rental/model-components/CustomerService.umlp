/* (c) https://github.com/MontiCore/monticore */

/**
 * This is a model-component for an application representing a car rental.
 *
 * Here we focus on customer-related information.
 */
import java.util.Optional;

umlp CustomerService {

  // TODO exercise 3 b)

  class Customer {
    String name;

    // Added
    String email;
  }

  // Added
  class VIPStatus {
    String personalGreeting;
  }

  // Added
  association [1] Customer (customer) -> (vipStatus) VIPStatus [0..1];
}
