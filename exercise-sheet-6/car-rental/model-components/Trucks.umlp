/* (c) https://github.com/MontiCore/monticore */
import java.time.LocalDateTime;

umlp Trucks {

    // TODO exercise 3 c)
    abstract class RentableItem;

    abstract class Vehicle extends RentableItem {
        String model;
        String maker;
        float costPerDay;
    }

    class Truck extends Vehicle{
        float tonnage;
    }

    class MileageDataPoint {
        LocalDateTime recordDate;
        float mileage;
    }

    class TruckMileageDataPoint extends MileageDataPoint {
        float longitude;
        float latitude;
    }

    association [1] Truck -> (dataPoint) TruckMileageDataPoint [*];

    enum RentableStatus {
        AVAILABLE, RENTED, MAINTENANCE;
    }

}
