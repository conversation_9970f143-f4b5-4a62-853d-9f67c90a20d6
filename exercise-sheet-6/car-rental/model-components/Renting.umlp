/* (c) https://github.com/MontiCore/monticore */

import java.time.LocalDateTime;

// One could also use "classdiagram Renting {", but the umlp tool prefers the
// first syntax
umlp Renting {

  // TODO exercise 3 a)

  abstract class RentableItem {
    String name;
    RentableStatus status;
    derived int available;
  }

  enum RentableStatus {
    AVAILABLE, RENTED;
  }

  class RentalTransaction {
    LocalDateTime rentalDate;
    float totalCost;
  }

  class Customer;

  association [*] RentalTransaction (transaction) <-> (customer) Customer [1];

  // Added
  association [0..1] RentalTransaction (transaction) -> (returnDate) LocalDateTime [0..1];

  class RentalService {
    RentalTransaction rentItem(RentableItem rentable);
    void returnItem(RentalTransaction rentalTransaction);

    // Added
    void processPayment(RentalTransaction rentalTransaction);
  }

  association [1] RentalService (rental) -> (item) RentableItem [*];
  association [1] RentalService (rental) -> (customer) Customer [*];

  // Added
  association [1] RentalService (rental) -> (transaction) RentalTransaction [*];

}
