/* (c) https://github.com/MontiCore/monticore */
import java.time.LocalDate;
import java.time.Period;
import java.util.List;

umlp Books {

  // TODO exercise 6

  // Abstract class representing any item that can be rented.
  abstract class RentableItem;

  // Class representing a book that can be rented.
  class Book extends RentableItem {
    String title;
    String isbn;
    LocalDate publicationDate;
    int pages;
  }

  enum Genre {
    FICTION, NONFICTION;
  }

  association [1] Book -> (genre) Genre [1];

  class Author {
    String name;
    String pseudonym;
    String nationality;
    LocalDate birthDate;
    LocalDate deathDate;
    List<String> awards;

    int getAge();
  }

  association [*] Book (book) <-> (author) Author [*];
}
