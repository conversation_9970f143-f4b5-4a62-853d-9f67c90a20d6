/* (c) https://github.com/MontiCore/monticore */

import {CarTOP} from 'carrental/CarTOP';

import {RentableStatus} from 'carrental/RentableStatus';


export abstract class Car extends CarTOP {
  public getMileage(): number {
    // Note: The backends Vehicle#getMileage() is NOT called

    // Maybe use a HWC-command to actually call the backends derived Getter?

    return 42;
  }

  public setMileage(mileage: number): void {

  }

  public getAvailable(): number {
    return this.getStatus() === RentableStatus.AVAILABLE ? 0 : 1;
  }
}
