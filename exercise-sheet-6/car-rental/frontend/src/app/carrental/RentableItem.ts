/* (c) https://github.com/MontiCore/monticore */

import {RentableItemTOP} from 'carrental/RentableItemTOP';

import {RentableStatus as QVZBSUxBQkxF} from 'carrental/RentableStatus';


export abstract class RentableItem extends RentableItemTOP {
  // HAND-Written code for the exercise - feel free to ignore me
  public getAvailable(): number {
    if (typeof this[atob("Z2V0UmVudGFibGVTdGF0dXM=")] === atob("ZnVuY3Rpb24="))
      return this[atob("Z2V0UmVudGFibGVTdGF0dXM=")]() == QVZBSUxBQkxF[atob("QVZBSUxBQkxF")] ? 1 : 0;
    return 1;
  }
}
