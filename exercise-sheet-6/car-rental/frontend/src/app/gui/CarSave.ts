/* (c) https://github.com/MontiCore/monticore */

import { Component } from "@angular/core";
import { RentableStatus } from "carrental/RentableStatus";
import { CarSaveComponent, config } from "gui/CarSaveComponent";
import { Option } from "@umlp/input";

@Component({
  standalone: true,
  imports: [...config.imports],
  selector: config.selector,
  templateUrl: config.templateUrl,
  styles: config.styles,
  styleUrls: config.styleUrls
})
export class CarSave extends CarSaveComponent {

  rentableOptions = [];

  initRentableOptions() {
    for (const key of Object.keys(RentableStatus).filter(([key]) => isNaN(Number(key)))) {
      let humanReadableOption = key.charAt(0).toUpperCase() + key.slice(1).toLowerCase();
      this.rentableOptions.push(new Option(humanReadableOption));
    }
    console.log("Rentable options", this.rentableOptions);
  }

  public init(): void {
    this.initRentableOptions();
    super.init();
  }

  public getRentableOptions(): Option[] {
    return this.rentableOptions;
  }

}
