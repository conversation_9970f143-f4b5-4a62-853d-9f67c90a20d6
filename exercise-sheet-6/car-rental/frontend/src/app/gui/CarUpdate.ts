/* (c) https://github.com/MontiCore/monticore */

import { Component } from "@angular/core";
import { CarUpdateComponent, config } from "gui/CarUpdateComponent";
import { Event } from "@umlp/common";

@Component({
  standalone: true,
  imports: [...config.imports],
  selector: config.selector,
  templateUrl: config.templateUrl,
  styles: config.styles,
  styleUrls: config.styleUrls
})
export class CarUpdate extends CarUpdateComponent {
  public override toOverview(): Event<void> {
    return $ => {
      this._router.navigate(['gui', 'CarOverview']);
    }
  }

  public override getImageUrl(maker: string): string {
    const img = localStorage.getItem(maker);
    if (!img) {
      return "assets/images/" + maker + ".jpg"
    }
    return img;
  }
}
