/* (c) https://github.com/MontiCore/monticore */

import { Component } from "@angular/core";
import { Car } from "carrental/Car";
import { DashboardComponent, config } from "gui/DashboardComponent";
import {
  GemBarChartData, GemBarChartEntry, GemBarChartEntryBuilder,
  GemLineChartData, GemLineChartEntry, GemLineChartEntryBuilder,
  GemPieChartData, GemPieChartEntry
} from "@umlp/charts";

@Component({
  standalone: true,
  imports: [...config.imports],
  selector: config.selector,
  templateUrl: config.templateUrl,
  styles: config.styles,
  styleUrls: config.styleUrls
})
export class Dashboard extends DashboardComponent {

  protected mileageIncrease: { data: GemLineChartData, hash: string } = { data: null, hash: '' };
  protected mileageMax: { data: GemBarChartData, hash: string } = { data: null, hash: '' };
  protected availability: { data: GemPieChartData, hash: string } = { data: null, hash: '' };

  public getMileageIncrease(cars: Car[]): GemLineChartData {
    // if data is not present return nothing
    if (!cars) {
      return null;
    }
    // create an array of dates to use them as x axis points later
    const dates: Date[] = [];

    for (const c of cars) {
      for (const mileage of c.getDataPointList()) {
        dates.push(mileage.getRecordDate());
      }
    }

    // sort dates
    dates.sort((a, b) => a > b ? 1 : -1);

    const labels: string[] = [];
    for (const date of dates) {
      const xAxisPoint = date.toDateString();
      if (!labels.includes(xAxisPoint)) {
        // use string representation of (non-repeating) dates as x axis points
        labels.push(xAxisPoint);
      }
    }

    const entries: GemLineChartEntry[] = []
    for (const c of cars) {
      // create an entry for each car:
      // label (1st arg) is the name of the car, data (2nd arg) are the mileage data for each date
      const entry = new GemLineChartEntryBuilder()
        .label(`${c.getMaker()} ${c.getModel()} ${c.getName()}`)
        .data(Array(labels.length).fill(0))
        .build();

      entries.push(entry);
      for (const mileage of c.getDataPointList()) {
        // find what position the mileage data needs to have to represent specific date
        const xAxisPoint = mileage.getRecordDate().toDateString();
        const xAxisPointIndex = labels.indexOf(xAxisPoint);
        entry._data[xAxisPointIndex] = mileage.getMileage();
      }
    }

    // optional: add interpolated itermediate points to entries where the data is not available
    for (const entry of entries) {
      for (let i = 0; i < entry._data.length; ++i) {
        const data = entry._data[i];
        if (data == null) {
          this.interpolateData(entry._data, i);
        }
      }
    }

    const data = new GemLineChartData(entries, labels);

    // Angular requirement
    // check if the calculated value is equal to the previous one
    const newHash = `${data.getEntriesList().map(e => e.hashCodeData())}_${data.hashCodeLabels()}`;
    if (this.mileageIncrease.hash !== newHash) {
      this.mileageIncrease.hash = newHash;
      this.mileageIncrease.data = data;
    }
    return this.mileageIncrease.data;
  }

  interpolateData(stat: number[], interpolatedIndex: number) {
    let left = 0;
    let leftI = 0;
    let right = 0;
    let rightI = 0;
    // find non-null point left of interpolated one
    for (let i = 0; i < stat.length; ++i) {
      if (stat[i] != null && interpolatedIndex > i) {
        left = stat[i];
        leftI = i;
      }
    }

    // find non-null point right of interpolated one
    for (let i = 0; i < stat.length; ++i) {
      if (stat[i] != null && interpolatedIndex < i) {
        right = stat[i];
        rightI = i;
        break;
      }
    }

    stat[interpolatedIndex] = left + ((right - left) / (rightI - leftI)) * (interpolatedIndex - leftI);
  }

  public getMileageMax(cars: Car[]): GemBarChartData {
    if (!cars) {
      return null;
    }

    // labels (x axis points) are car names
    const labels: string[] = [];
    for (const c of cars) {
      labels.push(`${c.getMaker()} ${c.getModel()} ${c.getName()}`);
    }

    // create an entry for each car
    const entries: GemBarChartEntry[] = [];
    for (const c of cars) {
      entries.push(
        new GemBarChartEntryBuilder()
          .label(c.getMaker())
          .data(Array(cars.length).fill(0))
          .build()
      )
    }

    for (let i = 0; i < cars.length; ++i) {
      // calculate average mileage for each car
      const c = cars[i];
      let maxMileage = 0;
      for (const m of c.getDataPointList()) {
        if (m.getMileage() > maxMileage) {
          maxMileage = m.getMileage();
        }
      }

      // find entry representing the car and set data for the matching index
      const entry = entries.find(e => e._label === c.getMaker());
      entry._data[i] = maxMileage;
    }

    const data = new GemBarChartData(entries, labels);

    // Angular requirement
    // check if the calculated value is equal to the previous one
    const newHash = `${data.getEntriesList().map(e => e.hashCodeData())}_${data.hashCodeLabels()}`;
    if (this.mileageMax.hash !== newHash) {
      this.mileageMax.hash = newHash;
      this.mileageMax.data = data;
    }
    return this.mileageMax.data;
  }

  public getAvailability(cars: Car[]): GemPieChartData {
    if (!cars) {
      return null;
    }

    // create 2 entries: for available and unavailable cars
    const entries: GemPieChartEntry[] = [
      new GemPieChartEntry(0, "Available cars", "green"),
      new GemPieChartEntry(0, "Unavailable cars", "red")
    ];
    let available = 0;
    let unavailable = 0;
    for (const c of cars) {
      if (c.getAvailable()) {
        available++;
      }
      else {
        unavailable++;
      }
    }
    entries[0]._value = available;
    entries[1]._value = unavailable;

    const data = new GemPieChartData(entries);

    // Angular requirement
    // check if the calculated value is equal to the previous one
    if (JSON.stringify(data) !== this.availability.hash) {
      // if data is new save the new state
      this.availability = { data: data, hash: JSON.stringify(data) };
    }
    return this.availability.data;
  }

}
