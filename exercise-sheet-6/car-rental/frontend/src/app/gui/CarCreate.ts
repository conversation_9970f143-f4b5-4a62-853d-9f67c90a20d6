/* (c) https://github.com/MontiCore/monticore */

import { Component } from "@angular/core";
import { Car } from "carrental/Car";
import { CarCreateComponent, config } from "gui/CarCreateComponent";
import { Event } from "@umlp/common";

@Component({
  standalone: true,
  imports: [...config.imports],
  selector: config.selector,
  templateUrl: config.templateUrl,
  styles: config.styles,
  styleUrls: config.styleUrls
})
export class CarCreate extends CarCreateComponent {

  public createAndNavigate(car: Car): Event<void> {
    return $ => {
      car.build();
      this._router.navigate(['gui', 'CarOverview']);
    };
  }

  public updateMaker(car: Car): Event<string> {
    return maker => {
      car.setMaker(maker);
    }
  }
}
