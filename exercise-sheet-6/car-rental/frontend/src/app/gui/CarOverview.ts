/* (c) https://github.com/MontiCore/monticore */

import { CarOverviewComponent, config } from "gui/CarOverviewComponent";
import { Component } from "@angular/core";
import { Car } from "carrental/Car";
import { Event, CommandManager } from "@umlp/common";
import { RentableStatus } from "carrental/RentableStatus";

@Component({
  standalone: true,
  imports: [...config.imports],
  selector: config.selector,
  templateUrl: config.templateUrl,
  styles: config.styles,
  styleUrls: config.styleUrls
})
export class CarOverview extends CarOverviewComponent {

  public getAvailableCarsTitle(): string {
    if (this.getCars() === undefined) {
      return "";
    }
    let i = 0;
    for (const c of this.getCars()) {
      if (c.getStatus() === RentableStatus.AVAILABLE) {
        i++;
      }
    }
    return i + " available cars";
  }

  public updateCar(car: Car): Event<void> {
    return $ => {
      this._router.navigate(['gui', 'CarUpdate', car.getGemId()]);
    }
  }

  public getImageUrl(maker: string): string {
    const img = localStorage.getItem(maker);
    if (!img) {
      return "assets/images/" + maker + ".jpg"
    }
    return img;
  }
}
