/* (c) https://github.com/MontiCore/monticore */
plugins {
  id 'java-library'

  id "com.github.node-gradle.node" version "7.1.0"
  id 'de.rwth.se.umlp-tool-frontend' version "$mc_version"
}

repositories {
  if (("true").equals(getProperty('useLocalRepo'))) {
    mavenLocal()
  }
  maven {
    credentials.username mavenUser
    credentials.password mavenPassword
    url repo
  }

  // native image
  maven { url "https://repo.spring.io/snapshot" }
  maven { url "https://repo.spring.io/milestone" }

  mavenCentral()
}

dependencies {
  implementation("umlp:umlp-rte:${mc_version}")
  testImplementation("com.h2database:h2:${h2_database}")

  implementation group: "de.monticore.lang", name: 'guidsl', version: mc_version
  implementation group: 'org.springframework.boot', name: 'spring-boot-starter-security', version: spring_version

  testImplementation 'org.junit.jupiter:junit-jupiter-api:5.9.2'
  testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.9.2'
}

node {
  download = true
  version = "${node_version}"
  nodeProxySettings = ProxySettings.OFF
  nodeProjectDir = file("${buildDir}/frontendGen")
}
nodeSetup.dependsOn(frontendSetup)

tasks.register('copyTags', Copy) {
  from(tasks.genDomain.getSymbolOutDir()) {
    include '**/*.tags'
  }
  into("$buildDir/tags")
}

tasks.register('generateCDTOGUI', UMLPCdToGuiTask) {
  setGroup('build')
  input = project(":backend").mergeCarRental.outputFile
  outputDir = layout.buildDirectory.dir("cd2gui")
  dependsOn(tasks.copyTags)
}

tasks.register('guiVariability', GuiVariabilityHandlerTask) {
  input.from(generateCDTOGUI.getOutputDir())
  input.from(file("../models/gui"))
  outputDir = layout.buildDirectory.dir("guiVariability")
  dependsOn(tasks.generateCDTOGUI)
}


genDomain {
  input = project(":backend").mergeCarRental.outputFile
}

createDomainSymbols {
  enabled = false // instead, we use the symbols from the backend (and thus the merged model!)
//  symbolOutDir = project(":backend").generateUmlpBackend.symbolOutDir
//  dependsOn(project(":backend").generateUmlpBackend.symbolOutDir)
}


genGUI {
  domainModels = ["CarRental"]
  guiCreateModels = ["gui.CarCreate"]
  guiCreateModelsAsFile = file(generateCDTOGUI.getOutputDir().file("meta/carrental/guiCreateTextModels"))
  // Override input, do not use from
  input.setFrom([{ // exclude singletons from CD2GUI #3983
    var guiVarOutputCollection = project.objects.fileCollection()
    var filteredFC = project.objects.fileCollection()
    guiVarOutputCollection.setFrom(file(tasks.guiVariability.getOutputDir()))
    guiVarOutputCollection.getAsFileTree().getFiles().stream()
        .filter(File::isFile)
        .filter {!it.getName().contains("RentalServiceOverview.gui")} // remove singletons
        .forEach {filteredFC.from(it)}
    return filteredFC
  }])
  // Use cd2gui symbols instead of cd2gui/symbols
  symbolDir += generateCDTOGUI.getOutputDir().file("symbols")
  symbolDir += file("$buildDir/tags")
  // and exclude the genDomain symbols (without gemId)
  var toExclude = tasks.genDomain.getSymbolOutDir().get().asFile
  symbolDir.setFrom symbolDir.filter(f -> f != toExclude)
  modelDir = file(tasks.guiVariability.getOutputDir())
  domainModels = ["CarRental"]
  dependsOn(tasks.guiVariability)
}

SYNC_genDomain {
  filter { line -> line.replaceAll('../../../build/umlp_frontend/intermediate/', '../../../build/frontendGen/build/') }
}
SYNC_genGUI {
  filter { line -> line.replaceAll('../../../build/umlp_frontend/intermediate/', '../../../build/frontendGen/build/') }
}

nodeSetup.dependsOn(frontendSetup)

npmInstall.args = ['--loglevel', 'error']

def prebuildTasks = [
  npmInstall, generate
]

task buildApp(type: NpxTask) {
  dependsOn(prebuildTasks)
  command = "ng"
  def projectId = System.getenv("CI_PROJECT_ID")
  if(projectId == null){
    args = ["build"]
  }else{
    // base route for automatic hosting @ https://studentprojects.se-rwth.de/sle25/...
    args = ["build", "--base-href=/sle25/" + projectId + "/"]
  }
}
assemble.dependsOn(buildApp)

task testApp(type: NpxTask) {
  dependsOn(prebuildTasks)
  command = "ng"
  args = ["test"]
}

task runApp(type: NpxTask) {
  dependsOn(prebuildTasks)
  command = "ng"
  args = ["serve"]
}

test {
  testLogging {
    events "PASSED", "SKIPPED", "FAILED", "STANDARD_OUT", "STANDARD_ERROR"
  }
  useJUnitPlatform()
}
