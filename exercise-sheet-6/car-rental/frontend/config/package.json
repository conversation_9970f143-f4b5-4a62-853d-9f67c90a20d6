{"name": "client", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "overrides": {"d3-array": "3.2.4"}, "dependencies": {"@angular/animations": "^17.0.7", "@angular/cdk": "^17.0.7", "@angular/common": "^17.0.7", "@angular/compiler": "^17.0.7", "@angular/core": "^17.0.7", "@angular/forms": "^17.0.7", "@angular/material": "^17.0.7", "@angular/platform-browser": "^17.0.7", "@angular/platform-browser-dynamic": "^17.0.7", "@angular/router": "^17.0.7", "@umlp/arrange": "^7.6.0", "@umlp/basic": "^7.6.0", "@umlp/charts": "^7.6.0", "@umlp/common": "^7.6.0", "@umlp/commonj2ts": "^7.6.0", "@umlp/input": "^7.6.0", "@umlp/navigation": "^7.6.0", "@umlp/table": "^7.6.0", "@umlp/theme": "^7.6.0", "font-awesome": "4.7.0", "moment": "~2.30.1", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.0", "many-keys-map": "2.0.1"}, "devDependencies": {"@angular-devkit/build-angular": "^17.0.7", "@angular/cli": "^17.0.7", "@angular/compiler-cli": "^17.0.7", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "puppeteer": "^23.4.1", "typescript": "~5.2.2"}}