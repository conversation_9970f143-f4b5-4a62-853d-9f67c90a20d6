import de.monticore.cdbasis._ast.ASTCDCompilationUnit;
import org.junit.jupiter.api.Assertions;
import umlp.UMLPMill;
import umlp._parser.UMLPParser;

import java.io.IOException;
import java.util.Optional;

public abstract class AbstractVerificationTest {

  protected void parseUMLP(String path) {
    UMLPParser parser = UMLPMill.parser();
    Optional<ASTCDCompilationUnit> optCompilationUnit = Optional.empty();
    try {
      optCompilationUnit = parser.parse(path);
    }
    catch (IOException e) {
      Assertions.fail("Failure while parsing the class diagram '" + path + "'.", e);
    }
    Assertions.assertTrue(optCompilationUnit.isPresent(), "Could not parse class diagram '" + path + "'");
  }

}
