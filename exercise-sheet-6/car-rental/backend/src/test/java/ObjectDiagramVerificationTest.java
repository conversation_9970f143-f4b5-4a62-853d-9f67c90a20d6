import de.monticore.od4development.OD4DevelopmentMill;
import de.monticore.od4development._parser.OD4DevelopmentParser;
import de.monticore.odbasis._ast.ASTODArtifact;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.Optional;

public class ObjectDiagramVerificationTest {

  @Test
  void verifyCarData() {
    String path = "../models/CarData.od";
    OD4DevelopmentParser parser = OD4DevelopmentMill.parser();

    Optional<ASTODArtifact> optODArtifact = Optional.empty();
    try {
      optODArtifact = parser.parse(path);
    }
    catch (IOException e) {
      Assertions.fail("Failure while parsing the object diagram '" + path + "'.", e);
    }
    Assertions.assertTrue(optODArtifact.isPresent(), "Could not parse object diagram '" + path + "'.");
  }

}
