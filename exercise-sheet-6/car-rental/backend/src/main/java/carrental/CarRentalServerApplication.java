/* (c) https://github.com/MontiCore/monticore */

package carrental;

import cardata.CarDataInstantiator;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.concurrent.ThreadLocalRandom;

@SpringBootApplication(
    // We have our own data management
    exclude = {DataSourceAutoConfiguration.class, HibernateJpaAutoConfiguration.class},
    scanBasePackages = {"umlp.backendrte.service.websocket", "umlp.backendrte.service.rest", "carrental.service", "service"}
)
public class CarRentalServerApplication extends CarRentalServerApplicationTOP {

  public static void main(String[] args) {
    SpringApplication.run(CarRentalServerApplication.class, args);
  }

  @PostConstruct
  @Override
  public void init() throws IOException {
    super.init();

    // add od data
    CarDataInstantiator cdi = new CarDataInstantiator();
    cdi.instantiate();
  }
}
