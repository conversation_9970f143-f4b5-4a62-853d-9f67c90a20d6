// Native image
buildscript {
  if (("true").equals(getProperty('buildNativeExecutable'))) {
    dependencies {
      classpath('com.google.cloud.tools:jib-native-image-extension-gradle:0.1.0')
    }
  }
}

plugins {
  id 'java-library'
  id 'de.rwth.se.umlp-tool-backend' version "${mc_version}"
  id 'de.se_rwth.codestyle' version "$mc_version_std" // SE-codestyle plugin

  id 'application'
  id 'com.google.cloud.tools.jib' version '3.4.4'
  id 'io.spring.dependency-management' version '1.0.15.RELEASE'
  id 'org.springframework.boot' version "${spring_version}"

  // native image
  id 'org.springframework.experimental.aot' version '0.12.2' apply false
}
// Only apply aot if native image is enabled
if (("true").equals(getProperty('buildNativeExecutable'))) {
  apply plugin: "org.springframework.experimental.aot"
}

repositories {
  mavenLocal()
  maven {
    credentials.username mavenUser
    credentials.password mavenPassword
    url repo
  }

  // native image
  maven { url "https://repo.spring.io/snapshot" }
  maven { url "https://repo.spring.io/milestone" }

  mavenCentral()
}

configurations {
  guiDefaultTypes
}

application {
  mainClass = 'carrental.CarRentalServerApplication'
}

if (!("true").equals(getProperty('buildNativeExecutable'))) {
  jib {
    container.mainClass = application.mainClass.get()
  }
}

dependencies {
  def withoutSpringLogging = {
    exclude module: 'spring-boot-starter-logging'
    exclude module: 'logback-classic'
    exclude module: 'log4j-to-slf4j'
  }

  guiDefaultTypes "de.monticore.lang:guidsl:${mc_version}:default-types"
  guiDefaultTypes "umlp.umlp-rte:client:${mc_version}"

  api group: 'umlp', name: 'umlp-rte', version: mc_version, withoutSpringLogging
  api group: 'umlp', name: 'umlp-test-rte', version: mc_version, withoutSpringLogging
  api 'com.h2database:h2:2.1.214'

  // Spring Boot Dependencies
  compileOnly group: 'org.springframework.boot', name: 'spring-boot-devtools', version: spring_version, withoutSpringLogging
  api group: 'org.springframework.boot', name: 'spring-boot-starter-web', version: spring_version, withoutSpringLogging
  api group: 'org.springframework.boot', name: 'spring-boot-starter-websocket', version: spring_version, withoutSpringLogging
  api group: 'org.springframework.boot', name: 'spring-boot-starter-webflux', version: spring_version, withoutSpringLogging
  api group: 'org.springframework.shell', name: 'spring-shell-starter', version: spring_shell_version, withoutSpringLogging
  //  implementation group: 'org.springframework.boot', name: 'spring-boot-starter-security', version: spring_version, withoutSpringLogging

  implementation group: 'org.slf4j', name: 'slf4j-simple', version: "1.7.10"

  testImplementation group: 'org.springframework.boot', name: 'spring-boot-starter-test', version: spring_version, withoutSpringLogging
  testImplementation 'org.junit.jupiter:junit-jupiter-api:5.9.2', withoutSpringLogging
  testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.9.2', withoutSpringLogging

  // import for verification tests
  testImplementation "umlp:umlp-tool:$mc_version"
  testImplementation "de.monticore.lang:cd4analysis:$mc_version_std"
  testImplementation "de.monticore.lang:od:$mc_version_std"
}

task mergeCarRental(type: UMLPMergeTask){
  source = file("../model-components/")
  includes = ["**.umlp"]
  excludes = ["Books.umlp"]

  // Where should the merged result be stored?
  outputFile = layout.buildDirectory.file("CarRental.umlp")  // buildDirectory = "build/"
}

task mergeBookRental(type: UMLPMergeTask){
  source = file("../model-components/")
  includes = ["**.umlp"]
  excludes = ["Cars.umlp", "Trucks.umlp"]

  // Where should the merged result be stored?
  outputFile = layout.buildDirectory.file("BookRental.umlp")  // buildDirectory = "build/"
}

generateUmlpBackend {
  input = mergeCarRental.outputFile
  symbolOutDir = file("${project.buildDir}/symbols")
  useSpring.set(true)
  useShell.set(true)
}

task genInitData(type: UMLPOD2DataTask) {
  input = file("../models/CarData.od")
  outputDir = file("$project.buildDir/initdata")
  hwcDir = file("$projectDir/src/main/java")
  symbolDir = files(generateUmlpBackend.symbolOutDir)
  dependsOn(generateUmlpBackend)
}


task runServer {
  dependsOn(bootRun {
    systemProperty "spring.shell.interactive.enabled", "false"
  })
}

task genTestGUI(type: UMLPGuiGenTask) {
  input = file("../models")
  outputDir = file("$project.buildDir/testgui")
  hwcDir = file("$projectDir/src/main/java")
  modelDir = layout.projectDirectory.dir("../models")
  symbolDir.from(generateUmlpBackend.symbolOutDir) // = symbolOutDir
  symbolDir.from(configurations.guiDefaultTypes) // += guiDefaultTypes
  symbolDir.from(configurations.umlpModulesDomainSymbols) // += umlpModulesDomainSymbols
  symbolDir.from(configurations.guiLib) // += guiLib
  domainModels = ["CarRental"]
  guiCreateModels = []
}

test {
  dependsOn("mergeBookRental", "mergeCarRental")

  outputs.cacheIf { false }

  useJUnitPlatform()

  // enable test logging when running Gradle from the command line
  testLogging {
    // log events
    events 'STARTED', 'SKIPPED', 'FAILED', 'PASSED'

    // log complete stacktraces
    exceptionFormat = 'full'

    // log System.out and System.err
    showStandardStreams = true
  }
}

sourceSets {
  main {
    java.srcDirs += [genInitData.outputDir]
  }
  test {
    java {
      exclude '**/gui/**'
    }
  }
}

compileJava.dependsOn(genTestGUI)

// native image
if (("true").equals(getProperty('buildNativeExecutable'))) {
  springAot {
    mainClass = application.mainClass.get()
  }

  def nativeImageBuildArgs = [
      "--initialize-at-build-time=org.slf4j.impl.SimpleLogger,org.slf4j.LoggerFactory,org.slf4j.simple.SimpleLogger,org.slf4j.impl.StaticLoggerBinder",
      "-H:IncludeResources=\"/.*\"",
      "-march=compatibility"
  ]

  // executable instead of library
  tasks.nativeCompile.options.get().sharedLibrary.set(false)

  graalvmNative.binaries.main {
    buildArgs(nativeImageBuildArgs)
  }

  bootBuildImage {
    imageName = "registry.git.rwth-aachen.de/monticore/umlp/native_image_demo"
    builder = "paketobuildpacks/builder:tiny"
    environment = [
        "BP_NATIVE_IMAGE"                : "true",
        "BP_NATIVE_IMAGE_BUILD_ARGUMENTS": nativeImageBuildArgs.join(" ")
    ]
  }

  jib {
    container.mainClass = application.mainClass.get()

    // Native image
    pluginExtensions {
      pluginExtension {
        implementation = 'com.google.cloud.tools.jib.gradle.extension.nativeimage.JibNativeImageExtension'
        properties = [
            imageName: tasks.nativeCompile.getExecutableName().get().toString()
        ]
      }
    }
  }
  tasks.jib.dependsOn tasks.nativeCompile
}

tasks.withType(Tar) {
  duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}
tasks.withType(Zip) {
  duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

// ignore spotless' formatting in Gradle's check task
spotless {
  enforceCheck = false
}
