j2ts=false
# buildNativeExecutable will be used in pipeline
buildNativeExecutable=false

# Overwrite the maven authentication for UMLP
mavenUser=sle23_01
mavenPassword=n3VHkD5Mxc
repo=https://nexus.se.rwth-aachen.de/content/groups/umlp

mc_version=7.8.0-sle25-SNAPSHOT
mc_version_std=7.8.0-SNAPSHOT
spring_version=2.7.3
h2_database=2.1.214
spring_shell_version=2.1.8
node_version=18.13.0
versions_version = 0.51.0

org.gradle.caching=false
org.gradle.parallel=true

# Set to true to load dependencies and plugins from ~/.m2(or another repo if you changed it manually)
useLocalRepo=false
org.gradle.console=plain
