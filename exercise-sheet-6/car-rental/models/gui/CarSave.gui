package gui;

import mc.fenix.basic.GemText;
import mc.fenix.basic.GemImage;
import mc.fenix.basic.GemButton;
import mc.fenix.input.GemTextInput;
import mc.fenix.input.GemDropdownInput;
import mc.fenix.input.GemTextInputTypes.Option;
import mc.fenix.arrange.GemColumn;
import mc.fenix.arrange.GemRow;
import mc.fenix.arrange.GemGrid;
import mc.fenix.arrange.GemCard;

import gui.navigation.NavContainer;

// data class
import CarRental.Car;

component CarSave(Car car, GUIViewElement image, GUIViewElement button) {
  @NavContainer(content = [
    @GemCard(title = "Card form", component =

      @GemRow(colGap = "20px", hAlign = "space-evenly", components = [
        image,
        @GemColumn(width = "40ch", rowGap = "5px", components = [
          @GemTextInput(
            labelText = "Model:",
            entry = car.model
          ),
          @GemTextInput(
            labelText = "Name:",
            entry = car.name
          ),
          @GemDropdownInput(
            labelText = "Status:",
            entry = car.status,
            options = getRentableOptions()
          ),
          @GemTextInput(
            labelText = "Cost per day:",
            inputType = "number",
            entry = car.costPerDay
          ),
          button
        ])
      ])

    )
  ]);
}
