package gui;

import mc.fenix.basic.GemText;
import mc.fenix.basic.GemImage;
import mc.fenix.basic.GemButton;
import mc.fenix.arrange.GemColumn;
import mc.fenix.arrange.GemRow;
import mc.fenix.arrange.GemGrid;
import mc.fenix.arrange.GemCard;
import mc.fenix.charts.GemLineChart;
import mc.fenix.charts.GemBarChart;
import mc.fenix.charts.GemPieChart;

import gui.navigation.NavContainer;

// data class
import CarRental.Car;

page Dashboard(List<Car> cars) {
  @NavContainer(content = [
    @GemRow(wrap = "wrap", rowGap = "10px", hAlign = "space-evenly", components = [

        @GemCard(
          width = "40%",
          title = "Mileage Data Points",
          component = @GemLineChart(data = getMileageIncrease(cars))
        ),

        @GemCard(
          width = "40%",
          title = "Total Mileage",
          component = @GemBarChart(data = getMileageMax(cars))
        ),

        @GemCard(
          height = "400px",
          title = "Availability",
          component = @GemPieChart(data = getAvailability(cars))
        )

    ])
  ]);
}
