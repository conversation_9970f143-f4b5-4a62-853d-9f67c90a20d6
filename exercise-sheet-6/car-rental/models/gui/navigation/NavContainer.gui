/* (c) https://github.com/MontiCore/monticore */
package gui.navigation;

import mc.fenix.arrange.GemRow;
import mc.fenix.arrange.GemColumn;

import mc.fenix.navigation.GemNavItem;
import mc.fenix.navigation.GemNavList;

component NavContainer(List<GUIViewElement> content) {
  // row of components, from left to right: navigation bar, main content.
  @GemRow(colGap = "25px", components = [

    // navigation bar
    @GemNavList(width = "40ch", height = "100vh", components = [
      @GemNavItem(title = "Home", target = "/gui/Start"),
      @GemNavItem(title = "Dashboard", target = "/gui/Dashboard"),
      @GemNavItem(title = "Simple car list", target = "/gui/SimpleCarList"),
      @GemNavItem(title = "Car overview", target = "/gui/CarOverview"),
      @GemNavItem(title = "New car", target = "/gui/CarCreate")
    ]),
    // main content
    @GemRow(hAlign = "center", components = content)

  ]);
}