plugins {
  id 'base'
  id 'com.github.ben-manes.versions' version "$versions_version" // check version updates
}

clean.dependsOn(subprojects.collect{it.getTasksByName("clean", false)})

build.dependsOn(subprojects.collect{ it.getTasksByName("build", false)})

if(("true").equals(getProperty('j2ts'))) {
  project('frontend') {
    sourceSets.main.java.srcDirs += ['../backend/src/main/java', '../backend/build/umlp/backend']
  }

  project('frontend').getTasksByName('translateJava2TSDomain', true).forEach {a -> {
    project('backend').getTasksByName('jar', true).forEach {b -> {
      a.dependsOn(b)
    }}
  }}
  project('frontend').getTasksByName('translateJava2TSGUI', true).forEach {a -> {
    project('backend').getTasksByName('jar', true).forEach {b -> {
      a.dependsOn(b)
    }}
  }}
}

task runAll {
  dependsOn ":frontend:runApp", ':backend:run'
}
