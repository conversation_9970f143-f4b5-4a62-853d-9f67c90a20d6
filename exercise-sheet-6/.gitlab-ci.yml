# (c) https://github.com/MontiCore/monticore
#
# This .gitlab-ci.yml file defines a GitLab CI/CD pipeline configuration for
# testing multiple exercises using Gradle.

# Use a Docker image with Gradle 7.6.4 and JDK 11 as the base environment for
# the pipeline.
image: registry.git.rwth-aachen.de/monticore/container-registry/gradle:7.6.4-jdk11

# Store the local Gradle build cache
cache:
  key: gradle-cache-key
  paths:
    - .gradle/caches
  policy: pull-push

# Set up environment variables and configurations to be executed before each job.
before_script:
  # Set Gradle user home to the current directory's .gradle folder.
  - export GRADLE_USER_HOME=`pwd`/.gradle

# Define stages for the pipeline.

stages: [ prepare, mergeCarRental, customerService, renting, trucks, objectDiagram, mergeBookRental, bookRental, test ]

# Prepare testing
testClasses:
  stage: prepare
  script:
    - gradle -p exercise-sheet-6/car-rental :backend:testClasses

# Merge BookRental
mergeCarRental:
  stage: prepare
  script:
    - gradle -p exercise-sheet-6/car-rental :backend:mergeCarRental

# Merge BookRental
mergeBookRental:
  stage: prepare
  script:
    - gradle -p exercise-sheet-6/car-rental :backend:mergeBookRental

# Verify the necessary conditions for the tasks and save the test reports.

verifyBookRental:
  stage: bookRental
  script: gradle -p exercise-sheet-6/car-rental :backend:test --tests BooksVerificationTest
  needs: [ mergeBookRental ]
  artifacts:
    when: always
    paths: [ '**/target/test-results/test/**/TEST-*.xml' ]
    reports:
      junit: '**/target/test-results/test/**/TEST-*.xml'

verifyCustomerService:
  stage: customerService
  script: gradle -p exercise-sheet-6/car-rental :backend:test --tests CustomerServiceVerificationTest
  needs: [ mergeCarRental ]
  artifacts:
    when: always
    paths: [ '**/target/test-results/test/**/TEST-*.xml' ]
    reports:
      junit: '**/target/test-results/test/**/TEST-*.xml'

verifyRenting:
  stage: renting
  script: gradle -p exercise-sheet-6/car-rental :backend:test --tests RentingVerificationTest
  needs: [ mergeCarRental ]
  artifacts:
    when: always
    paths: [ '**/target/test-results/test/**/TEST-*.xml' ]
    reports:
      junit: '**/target/test-results/test/**/TEST-*.xml'

verifyTrucks:
  stage: trucks
  script: gradle -p exercise-sheet-6/car-rental :backend:test --tests TrucksVerificationTest
  needs: [ mergeCarRental ]
  artifacts:
    when: always
    paths: [ '**/target/test-results/test/**/TEST-*.xml' ]
    reports:
      junit: '**/target/test-results/test/**/TEST-*.xml'

verifyObjectDiagram:
  stage: objectDiagram
  script: gradle -p exercise-sheet-6/car-rental :backend:test --tests ObjectDiagramVerificationTest
  needs: [ mergeCarRental ]
  artifacts:
    when: always
    paths: [ '**/target/test-results/test/**/TEST-*.xml' ]
    reports:
      junit: '**/target/test-results/test/**/TEST-*.xml'

# Deploy the web frontend
montigem:
  image: registry.git.rwth-aachen.de/monticore/container-registry/gradle:7-native-image # needed for native-image
  stage: test
  script:
    - cd exercise-sheet-6/car-rental
    - "gradle build --refresh-dependencies -P buildNativeExecutable=true"
    # Backend Container
    - "gradle :backend:jib -P buildNativeExecutable=true \
        -Djib.to.image=$CI_REGISTRY_IMAGE/backend:latest \
        -Djib.to.auth.username=$CI_REGISTRY_USER  \
        -Djib.to.auth.password=$CI_REGISTRY_PASSWORD"
    # Frontend Container
    - "gradle :frontend:deployImageCI -P buildNativeExecutable=true"
  rules:
    - when: on_success
  artifacts: # upload the junit test results
    when: always
    paths: [ '**/target/test-results/test/' ]
    reports:
      junit: '**/target/test-results/test/**/TEST-*.xml'
